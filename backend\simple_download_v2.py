"""
Simple OpenVoice V2 model downloader
"""

import requests
import zipfile
from pathlib import Path

def download_v2_models():
    models_dir = Path("models/openvoice")
    models_dir.mkdir(parents=True, exist_ok=True)
    
    url = "https://myshell-public-repo-hosting.s3.amazonaws.com/openvoice/checkpoints_v2_0417.zip"
    zip_path = models_dir / "checkpoints_v2_0417.zip"
    
    print("📥 Downloading OpenVoice V2 models...")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(zip_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r📊 Progress: {progress:.1f}%", end='', flush=True)
        
        print(f"\n✅ Downloaded {zip_path.name} ({downloaded / (1024*1024):.1f} MB)")
        
        # Extract
        print("📦 Extracting models...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(models_dir)
        
        print("✅ Models extracted successfully!")
        print("🎉 OpenVoice V2 models ready!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    download_v2_models()
