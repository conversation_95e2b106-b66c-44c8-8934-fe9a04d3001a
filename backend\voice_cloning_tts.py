"""
Professional Voice Cloning & Text-to-Speech Program - REFINED VERSION
Author: AI Assistant
Description: High-fidelity voice cloning with minimal processing for natural sound
Features: Accurate voice cloning, gentle enhancement, quality preservation
"""

import os
import sys
import warnings
warnings.filterwarnings("ignore")

print("""
╔═══════════════════════════════════════════════════════════════╗
║        High-Fidelity Voice Cloning & TTS Studio v4.0          ║
╠═══════════════════════════════════════════════════════════════╣
║   Initializing system with quality-first approach...          ║
╚═══════════════════════════════════════════════════════════════╝
""")

# Core dependencies only
def check_core_dependencies():
    core_packages = [
        ('torch', 'torch'),
        ('torchaudio', 'torchaudio'), 
        ('TTS', 'TTS'),
        ('gradio', 'gradio'),
        ('numpy', 'numpy'),
        ('scipy', 'scipy'),
        ('librosa', 'librosa'),
        ('soundfile', 'soundfile')
    ]
    
    missing = []
    for package_name, import_name in core_packages:
        try:
            __import__(import_name)
            print(f"✓ {package_name} is installed")
        except ImportError:
            missing.append(package_name)
            print(f"✗ {package_name} is missing")
    
    if missing:
        print(f"\n📦 Installing core packages: {', '.join(missing)}")
        for package in missing:
            os.system(f"{sys.executable} -m pip install {package}")
    
    return len(missing) == 0

check_core_dependencies()

# Import core modules
import torch
import torchaudio
import numpy as np
import tempfile
import librosa
import soundfile as sf
from scipy import signal
from scipy.signal import butter, filtfilt
from scipy.io import wavfile

# Ensure soundfile is available globally
try:
    import soundfile
    SF_AVAILABLE = True
except ImportError:
    SF_AVAILABLE = False
    print("⚠️ Soundfile not available")

# Try to import pydub for format conversion
try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
    print("✅ Pydub available for audio format conversion")
except ImportError:
    PYDUB_AVAILABLE = False
    print("⚠️ Pydub not available - limited format support")

# Fix for PyTorch compatibility
torch.serialization.add_safe_globals(['TTS.tts.configs.xtts_config.XttsConfig'])

# Monkey patch for torch.load
original_load = torch.load
def patched_load(*args, **kwargs):
    kwargs['weights_only'] = False
    return original_load(*args, **kwargs)
torch.load = patched_load

# Import TTS
from TTS.api import TTS

def convert_audio_format(input_path, output_path=None):
    """Convert audio file to WAV format using pydub if available"""
    if not PYDUB_AVAILABLE:
        return input_path

    try:
        # Detect format and convert
        if input_path.lower().endswith(('.webm', '.ogg')):
            print(f"🔄 Converting {input_path} to WAV format...")
            audio = AudioSegment.from_file(input_path)

            if output_path is None:
                output_path = tempfile.mktemp(suffix='.wav')

            # Export as WAV
            audio.export(output_path, format="wav")
            print(f"✅ Converted to: {output_path}")
            return output_path
        else:
            return input_path
    except Exception as e:
        print(f"⚠️ Format conversion failed: {str(e)}")
        return input_path

class HighFidelityAudioProcessor:
    """Advanced, high-quality audio processing to preserve voice fidelity and enhance realism"""

    def __init__(self, max_duration=60.0):
        self.target_sr = 24000  # XTTS native sample rate
        self.min_duration = 3.0  # Minimum duration for quality cloning
        self.max_duration = max_duration  # Maximum duration - configurable for better quality
        print(f"📏 Audio processor configured: max duration = {self.max_duration}s")

    def _select_best_audio_segment(self, audio, sr, target_duration):
        """Intelligently select the best audio segment for voice cloning"""
        target_samples = int(target_duration * sr)

        if len(audio) <= target_samples:
            return audio

        print("🎯 Analyzing audio to find the best segment for voice cloning...")

        # Method 1: Find segment with most consistent speech (least silence)
        hop_length = 512
        frame_length = 2048

        # Calculate energy and spectral features
        energy = librosa.feature.rms(y=audio, frame_length=frame_length, hop_length=hop_length)[0]
        spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sr, hop_length=hop_length)[0]

        # Calculate speech activity (combination of energy and spectral content)
        speech_activity = energy * (spectral_centroids / np.max(spectral_centroids))

        # Use sliding window to find best segment
        window_frames = int(target_samples / hop_length)
        best_start = 0
        best_score = 0

        for i in range(len(speech_activity) - window_frames):
            window_activity = speech_activity[i:i + window_frames]

            # Score based on:
            # 1. Average activity level
            # 2. Consistency (low variance)
            # 3. Avoid very quiet or very loud segments
            avg_activity = np.mean(window_activity)
            consistency = 1.0 / (1.0 + np.var(window_activity))

            # Prefer moderate activity levels (avoid silence and shouting)
            activity_preference = 1.0 - abs(avg_activity - np.median(speech_activity))

            score = avg_activity * consistency * activity_preference

            if score > best_score:
                best_score = score
                best_start = i

        # Convert back to sample indices
        start_sample = best_start * hop_length
        end_sample = start_sample + target_samples

        # Ensure we don't go out of bounds
        if end_sample > len(audio):
            end_sample = len(audio)
            start_sample = end_sample - target_samples

        selected_audio = audio[start_sample:end_sample]

        start_time = start_sample / sr
        end_time = end_sample / sr
        print(f"✅ Selected segment: {start_time:.1f}s - {end_time:.1f}s (best speech quality)")

        return selected_audio

    def prepare_reference_audio(self, audio_path, enhance=False):
        """Prepare reference audio with advanced processing for optimal cloning"""
        print("📊 Analyzing reference audio...")

        # Try to convert format if needed
        converted_path = convert_audio_format(audio_path)
        cleanup_converted = converted_path != audio_path
        if cleanup_converted:
            print(f"🔄 Using converted file: {converted_path}")
            audio_path = converted_path

        # Load at native sample rate with better error handling
        try:
            # Try loading with librosa first
            audio, sr = librosa.load(audio_path, sr=None, mono=True)
            original_duration = len(audio) / sr
            print(f"Original audio: {original_duration:.2f}s at {sr}Hz")
        except Exception as e:
            print(f"❌ Error loading audio file with librosa: {str(e)}")
            print(f"File path: {audio_path}")
            print(f"File exists: {os.path.exists(audio_path)}")
            if os.path.exists(audio_path):
                print(f"File size: {os.path.getsize(audio_path)} bytes")

            # Try alternative loading methods for WebM/other formats
            try:
                print("🔄 Trying alternative audio loading method...")
                # Use soundfile module directly
                audio, sr = soundfile.read(audio_path, dtype='float32')
                if len(audio.shape) > 1:  # Convert stereo to mono
                    audio = np.mean(audio, axis=1)
                original_duration = len(audio) / sr
                print(f"✅ Loaded with soundfile: {original_duration:.2f}s at {sr}Hz")
            except Exception as e2:
                print(f"❌ Soundfile also failed: {str(e2)}")
                raise Exception(f"Failed to load audio file with both librosa and soundfile. Original error: {str(e)}, Soundfile error: {str(e2)}")

        # Quality checks
        if original_duration < self.min_duration:
            print(f"⚠️ Warning: Audio is {original_duration:.1f}s, recommend at least {self.min_duration}s for best quality")
        elif original_duration > self.max_duration:
            print(f"📏 Audio is {original_duration:.1f}s, selecting best {self.max_duration}s for optimal processing")
            # Don't just truncate - select the best segment
            audio = self._select_best_audio_segment(audio, sr, self.max_duration)

        # Only resample if necessary
        if sr != self.target_sr:
            print(f"Resampling from {sr}Hz to {self.target_sr}Hz...")
            audio = librosa.resample(audio, orig_sr=sr, target_sr=self.target_sr)
            sr = self.target_sr

        # Advanced audio analysis for quality optimization
        audio = self.optimize_for_cloning(audio, sr, enhance)

        # Save with high quality
        temp_path = tempfile.mktemp(suffix='.wav')
        try:
            soundfile.write(temp_path, audio, sr, subtype='PCM_24')  # Higher bit depth for better quality
        except Exception as e:
            print(f"⚠️ Error saving audio with PCM_24, trying PCM_16: {e}")
            soundfile.write(temp_path, audio, sr, subtype='PCM_16')

        # Clean up converted file if it was created
        if cleanup_converted and os.path.exists(converted_path):
            try:
                os.unlink(converted_path)
                print(f"🗑️ Cleaned up converted file: {converted_path}")
            except:
                pass  # Ignore cleanup errors

        return temp_path
    
    def normalize_audio(self, audio):
        """Gentle normalization to prevent clipping"""
        max_val = np.max(np.abs(audio))
        if max_val > 0:
            # Normalize to 95% to leave headroom
            audio = audio * (0.95 / max_val)
        return audio
    
    def optimize_for_cloning(self, audio, sr, enhance=False):
        """Advanced audio optimization for better voice cloning results"""
        print("🔧 Optimizing audio for voice cloning...")

        # Step 1: Intelligent normalization
        audio = self.smart_normalize(audio)

        # Step 2: Voice activity detection and trimming
        audio = self.trim_silence_intelligent(audio, sr)

        # Step 3: Optional enhancement
        if enhance:
            audio = self.advanced_enhance(audio, sr)

        # Step 4: Ensure optimal length and quality
        audio = self.ensure_optimal_length(audio, sr)

        return audio

    def smart_normalize(self, audio):
        """Intelligent normalization that preserves dynamics"""
        # Calculate RMS for better normalization
        rms = np.sqrt(np.mean(audio**2))
        if rms > 0:
            # Target RMS around -20dB for optimal voice cloning
            target_rms = 0.1  # -20dB
            audio = audio * (target_rms / rms)

        # Prevent clipping while maintaining dynamics
        max_val = np.max(np.abs(audio))
        if max_val > 0.95:
            audio = audio * (0.95 / max_val)

        return audio

    def trim_silence_intelligent(self, audio, sr):
        """Intelligent silence trimming that preserves natural speech patterns"""
        # Use librosa's voice activity detection
        intervals = librosa.effects.split(audio, top_db=30, frame_length=2048, hop_length=512)

        if len(intervals) > 0:
            # Keep some padding for natural speech
            start_padding = int(0.1 * sr)  # 100ms
            end_padding = int(0.1 * sr)    # 100ms

            start_sample = max(0, intervals[0][0] - start_padding)
            end_sample = min(len(audio), intervals[-1][1] + end_padding)

            audio = audio[start_sample:end_sample]

        return audio

    def advanced_enhance(self, audio, sr):
        """Advanced but gentle enhancement for voice clarity"""
        # Gentle high-pass to remove subsonic noise
        nyquist = sr / 2
        low_cutoff = 80 / nyquist  # Remove below 80Hz

        if low_cutoff < 1.0:
            b, a = butter(3, low_cutoff, btype='high')
            audio = filtfilt(b, a, audio)

        # Gentle de-essing (reduce harsh sibilants)
        audio = self.gentle_deess(audio, sr)

        return audio

    def gentle_deess(self, audio, sr):
        """Gentle de-essing to reduce harsh sibilants"""
        # Simple de-essing using dynamic range compression on high frequencies
        nyquist = sr / 2
        high_cutoff = 6000 / nyquist  # Focus on sibilant range

        if high_cutoff < 1.0:
            # Extract high frequencies
            b, a = butter(2, high_cutoff, btype='high')
            high_freq = filtfilt(b, a, audio)

            # Gentle compression on high frequencies
            compressed_high = np.sign(high_freq) * np.power(np.abs(high_freq), 0.8)

            # Mix back with original
            low_freq = audio - high_freq
            audio = low_freq + compressed_high * 0.7

        return audio

    def ensure_optimal_length(self, audio, sr):
        """Ensure audio length is optimal for voice cloning"""
        duration = len(audio) / sr

        # If too short, we can't extend it meaningfully, just warn
        if duration < self.min_duration:
            print(f"⚠️ Audio duration {duration:.1f}s is below recommended {self.min_duration}s")

        # If too long, keep the most speech-dense portion
        if duration > self.max_duration:
            target_samples = int(self.max_duration * sr)

            # Find the most active portion
            hop_length = 512
            frame_length = 2048
            energy = librosa.feature.rms(y=audio, frame_length=frame_length, hop_length=hop_length)[0]

            # Use a sliding window to find the most active segment
            window_frames = int(target_samples / hop_length)
            if len(energy) > window_frames:
                best_start = 0
                best_energy = 0

                for i in range(len(energy) - window_frames):
                    window_energy = np.sum(energy[i:i + window_frames])
                    if window_energy > best_energy:
                        best_energy = window_energy
                        best_start = i

                start_sample = best_start * hop_length
                audio = audio[start_sample:start_sample + target_samples]
                print(f"📏 Selected most active {self.max_duration}s segment")

        return audio
    
    def remove_silence(self, audio, sr, threshold_db=40):
        """Remove only long silences, preserve natural pauses"""
        intervals = librosa.effects.split(audio, top_db=threshold_db)
        
        if len(intervals) > 0:
            # Keep all audio segments with natural timing
            result = []
            for start, end in intervals:
                result.append(audio[start:end])
                # Add tiny gap between segments
                if len(result) > 1:
                    result.append(np.zeros(int(0.05 * sr)))
            
            return np.concatenate(result) if result else audio
        return audio
    
    def process_generated_audio(self, audio_path, remove_silence_flag=False):
        """Minimal post-processing to preserve quality"""
        print("🎵 Applying minimal quality refinement...")
        
        # Load audio
        audio, sr = librosa.load(audio_path, sr=None, mono=True)
        
        # Only normalize
        audio = self.normalize_audio(audio)
        
        # Remove silence only if requested
        if remove_silence_flag:
            audio = self.remove_silence(audio, sr)
        
        # Gentle fade in/out to prevent clicks (2ms)
        fade_samples = int(0.002 * sr)
        if len(audio) > fade_samples * 2:
            audio[:fade_samples] *= np.linspace(0, 1, fade_samples)
            audio[-fade_samples:] *= np.linspace(1, 0, fade_samples)
        
        # Save
        output_path = tempfile.mktemp(suffix='.wav')
        soundfile.write(output_path, audio, sr, subtype='PCM_16')

        return output_path

class HighFidelityVoiceCloning:
    def __init__(self):
        """Initialize High-Fidelity Voice Cloning System"""
        print("🚀 Initializing High-Fidelity Voice Cloning...")
        
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"Using device: {self.device}")
        
        # Initialize audio processor with configurable max duration
        # Options: 30.0 (conservative), 60.0 (balanced), 120.0 (high quality), 300.0 (maximum quality)
        max_audio_duration = 60.0  # Increased from 30s for better quality
        self.audio_processor = HighFidelityAudioProcessor(max_duration=max_audio_duration)
        
        try:
            # Load XTTS with optimal settings
            print("Loading XTTS v2 model...")
            self.tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=(self.device == "cuda"))

            # Configure for maximum quality and realism
            if hasattr(self.tts, 'synthesizer') and hasattr(self.tts.synthesizer, 'tts_model'):
                config = getattr(self.tts.synthesizer.tts_model, 'config', None)
                if config:
                    # Enhanced settings for better voice matching and realism
                    config.temperature = 0.75  # Slightly higher for more natural variation
                    config.length_penalty = 1.0
                    config.repetition_penalty = 10.0  # Higher to reduce repetition artifacts
                    config.top_k = 50
                    config.top_p = 0.85  # Balanced for quality and diversity
                    config.do_sample = True

                    # Additional quality settings if available
                    if hasattr(config, 'decoder_attention_dropout'):
                        config.decoder_attention_dropout = 0.0  # Reduce dropout for better quality
                    if hasattr(config, 'encoder_attention_dropout'):
                        config.encoder_attention_dropout = 0.0

                    print("✅ Model configured for maximum quality and realism")

            # Set up advanced synthesis parameters
            self.synthesis_params = {
                'temperature': 0.75,
                'length_penalty': 1.0,
                'repetition_penalty': 10.0,
                'top_k': 50,
                'top_p': 0.85,
                'speed': 1.0,  # Natural speaking speed
                'enable_text_splitting': True,  # Better handling of long texts
            }

            print("✅ Model loaded successfully with enhanced configuration!")

        except Exception as e:
            print(f"Error loading model: {e}")
            raise
        
        self.current_voice_path = None
        self.processed_voice_path = None
        self.sample_text = "Hello! This is a test of the voice cloning system. The quality should sound natural and clear."
        
    def clone_voice(self, audio_file, enhancement_level="none", max_duration=None):
        """Clone voice with focus on quality preservation"""
        try:
            if audio_file is None:
                return None, None, "Please upload an audio file first!"
            
            print(f"\n{'='*50}")
            print(f"🎯 Starting high-fidelity voice cloning...")
            print(f"Enhancement: {enhancement_level}")
            print(f"{'='*50}\n")
            
            # Temporarily adjust max duration if specified
            original_max_duration = self.audio_processor.max_duration
            if max_duration is not None:
                self.audio_processor.max_duration = max_duration
                print(f"🔧 Temporarily set max duration to {max_duration}s for this cloning")

            try:
                # Process reference with minimal changes
                apply_enhancement = enhancement_level != "none"
                processed_audio = self.audio_processor.prepare_reference_audio(
                    audio_file,
                    enhance=apply_enhancement
                )
            finally:
                # Restore original max duration
                self.audio_processor.max_duration = original_max_duration
            
            self.processed_voice_path = processed_audio
            self.current_voice_path = audio_file
            
            # Generate sample with high quality
            print("🎤 Generating voice sample...")
            sample_output = self._generate_high_quality_audio(
                self.sample_text, 
                processed_audio,
                quality_preset="high"
            )
            
            if sample_output:
                # Minimal post-processing
                sample_final = self.audio_processor.process_generated_audio(
                    sample_output,
                    remove_silence_flag=False
                )
                
                # Get audio info
                info = soundfile.info(audio_file)
                duration = info.duration
                
                info_text = f"📊 Voice Profile:\n"
                info_text += f"• Duration: {duration:.1f}s\n"
                info_text += f"• Sample Rate: {info.samplerate}Hz\n"
                info_text += f"• Processing: {'Minimal Enhancement' if apply_enhancement else 'None (Pure)'}\n"
                info_text += f"• Quality: Maximum Fidelity"
                
                return sample_final, info_text, "✅ Voice cloned successfully with high fidelity!"
            else:
                return None, None, "❌ Error during voice cloning"
                
        except Exception as e:
            print(f"Error: {str(e)}")
            return None, None, f"❌ Error: {str(e)}"
    
    def _generate_high_quality_audio(self, text, reference_audio, quality_preset="high", emotion="neutral", speed=1.0):
        """Generate audio with maximum quality settings and emotion control"""
        try:
            # Create output file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
                output_path = tmp_file.name

            print(f"🎙️ Synthesizing speech with {quality_preset} quality...")
            print(f"🎭 Emotion: {emotion}, Speed: {speed}x")

            # Prepare text for optimal synthesis
            processed_text = self._preprocess_text_for_synthesis(text, emotion)

            # Dynamic quality settings based on preset
            synthesis_kwargs = {
                'text': processed_text,
                'speaker_wav': reference_audio,
                'language': 'en',
                'file_path': output_path,
                'split_sentences': True,  # Always split for better quality
            }

            # Apply quality-specific settings
            if quality_preset == "high":
                # Maximum quality settings
                synthesis_kwargs.update({
                    'temperature': self.synthesis_params['temperature'],
                    'length_penalty': self.synthesis_params['length_penalty'],
                    'repetition_penalty': self.synthesis_params['repetition_penalty'],
                    'top_k': self.synthesis_params['top_k'],
                    'top_p': self.synthesis_params['top_p'],
                    'speed': speed,
                })
            elif quality_preset == "standard":
                # Standard quality (faster)
                synthesis_kwargs.update({
                    'temperature': 0.85,  # Higher for more variation
                    'length_penalty': 0.8,
                    'repetition_penalty': 8.0,
                    'top_k': 60,
                    'top_p': 0.9,
                    'speed': speed,
                })
            elif quality_preset == "ultra":
                # Ultra-high quality (slower but better)
                synthesis_kwargs.update({
                    'temperature': 0.65,  # Lower for more consistency
                    'length_penalty': 1.2,
                    'repetition_penalty': 15.0,  # Higher to prevent artifacts
                    'top_k': 40,
                    'top_p': 0.8,
                    'speed': speed,
                })

            # Generate with error handling and retries
            max_retries = 2
            for attempt in range(max_retries + 1):
                try:
                    # Filter out unsupported parameters for the current TTS version
                    filtered_kwargs = self._filter_synthesis_kwargs(synthesis_kwargs)
                    self.tts.tts_to_file(**filtered_kwargs)
                    break
                except Exception as e:
                    if attempt < max_retries:
                        print(f"⚠️ Synthesis attempt {attempt + 1} failed, retrying...")
                        # Fallback to simpler parameters
                        synthesis_kwargs = {
                            'text': processed_text,
                            'speaker_wav': reference_audio,
                            'language': 'en',
                            'file_path': output_path,
                            'split_sentences': True,
                        }
                    else:
                        raise e

            return output_path
                
        except Exception as e:
            print(f"Synthesis error: {str(e)}")
            # Fallback method
            try:
                wav = self.tts.tts(
                    text=text,
                    speaker_wav=reference_audio,
                    language="en"
                )
                wavfile.write(output_path, 24000, (wav * 32767).astype(np.int16))
                return output_path
            except:
                return None

    def _preprocess_text_for_synthesis(self, text, emotion="neutral"):
        """Preprocess text for better synthesis quality"""
        # Clean up text
        text = text.strip()

        # Add emotion-based modifications
        if emotion == "excited":
            # Add slight emphasis markers (some TTS models respond to these)
            text = text.replace(".", "!")
        elif emotion == "calm":
            # Ensure proper punctuation for calm delivery
            if not text.endswith(('.', '!', '?')):
                text += "."
        elif emotion == "questioning":
            if not text.endswith('?'):
                text += "?"

        # Ensure proper sentence structure
        if not text.endswith(('.', '!', '?')):
            text += "."

        return text

    def _filter_synthesis_kwargs(self, kwargs):
        """Filter synthesis kwargs to only include supported parameters"""
        # Base parameters that are always supported
        base_params = ['text', 'speaker_wav', 'language', 'file_path', 'split_sentences']
        filtered = {k: v for k, v in kwargs.items() if k in base_params}

        # Try to include advanced parameters if supported
        advanced_params = ['temperature', 'length_penalty', 'repetition_penalty', 'top_k', 'top_p', 'speed']

        # Check if the TTS instance supports these parameters
        if hasattr(self.tts, 'synthesizer') and hasattr(self.tts.synthesizer, 'tts_model'):
            for param in advanced_params:
                if param in kwargs:
                    filtered[param] = kwargs[param]

        return filtered

    def _apply_advanced_settings(self, advanced_settings):
        """Apply advanced settings to the synthesis parameters"""
        if not advanced_settings:
            return

        # Update synthesis parameters with advanced settings (handle both dict and object)
        if isinstance(advanced_settings, dict):
            if 'temperature' in advanced_settings:
                self.synthesis_params['temperature'] = advanced_settings['temperature']
            if 'repetition_penalty' in advanced_settings:
                self.synthesis_params['repetition_penalty'] = advanced_settings['repetition_penalty']
            if 'length_penalty' in advanced_settings:
                self.synthesis_params['length_penalty'] = advanced_settings['length_penalty']
            if 'top_k' in advanced_settings:
                self.synthesis_params['top_k'] = advanced_settings['top_k']
            if 'top_p' in advanced_settings:
                self.synthesis_params['top_p'] = advanced_settings['top_p']
        else:
            # Handle object-style settings
            if hasattr(advanced_settings, 'temperature') and advanced_settings.temperature is not None:
                self.synthesis_params['temperature'] = advanced_settings.temperature
            if hasattr(advanced_settings, 'repetition_penalty') and advanced_settings.repetition_penalty is not None:
                self.synthesis_params['repetition_penalty'] = advanced_settings.repetition_penalty
            if hasattr(advanced_settings, 'length_penalty') and advanced_settings.length_penalty is not None:
                self.synthesis_params['length_penalty'] = advanced_settings.length_penalty
            if hasattr(advanced_settings, 'top_k') and advanced_settings.top_k is not None:
                self.synthesis_params['top_k'] = advanced_settings.top_k
            if hasattr(advanced_settings, 'top_p') and advanced_settings.top_p is not None:
                self.synthesis_params['top_p'] = advanced_settings.top_p

        print(f"🔧 Applied advanced settings: {self.synthesis_params}")

    def generate_tts(self, text, quality_mode="high", remove_silence=False, emotion="neutral", speed=1.0, advanced_settings=None):
        """Generate TTS with quality focus"""
        try:
            if not self.current_voice_path:
                return None, "❌ Please clone a voice first!"
            
            if not text.strip():
                return None, "❌ Please enter some text!"
            
            print(f"\n{'='*50}")
            print(f"🎯 Generating high-fidelity speech...")
            print(f"Quality mode: {quality_mode}, Emotion: {emotion}, Speed: {speed}x")
            print(f"{'='*50}\n")

            # Use processed voice
            reference = self.processed_voice_path or self.current_voice_path

            # Clean text
            text = text.strip()

            # Apply advanced settings if provided
            if advanced_settings:
                self._apply_advanced_settings(advanced_settings)
                print(f"🔧 Applied advanced settings: {advanced_settings}")

            # Generate with enhanced quality and emotion control
            output_path = self._generate_high_quality_audio(
                text,
                reference,
                quality_preset=quality_mode,
                emotion=emotion,
                speed=speed
            )
            
            if output_path and os.path.exists(output_path):
                # Minimal post-processing
                final_output = self.audio_processor.process_generated_audio(
                    output_path,
                    remove_silence_flag=remove_silence
                )
                
                # Get stats
                duration = librosa.get_duration(filename=final_output)
                word_count = len(text.split())
                
                # Cleanup
                if output_path != final_output:
                    os.unlink(output_path)
                
                return final_output, f"✅ Generated {word_count} words | {duration:.1f}s | High Fidelity"
            else:
                return None, "❌ Error generating speech"
                
        except Exception as e:
            print(f"TTS error: {str(e)}")
            return None, f"❌ Error: {str(e)}"

# Gradio interface code removed - using FastAPI instead
# Only the core classes (HighFidelityVoiceCloning, HighFidelityAudioProcessor) are needed for the API